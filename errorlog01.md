Error getting public playlist videos: GaxiosError: API key expired. Please renew the API key.
    at Gaxios._request (C:\Users\<USER>\Desktop\Chambitas\zonacero\node_modules\gaxios\build\cjs\src\gaxios.js:154:23)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async YouTubeService.getPublicPlaylistVideos (C:\Users\<USER>\Desktop\Chambitas\zonacero\backend\youtube-service.js:198:30)
    at async C:\Users\<USER>\Desktop\Chambitas\zonacero\backend\server.js:977:18 {
  config: {
    url: URL {
      href: 'https://youtube.googleapis.com/youtube/v3/playlistItems?part=snippet%2CcontentDetails%2Cstatus&playlistId=PLIddAt-uiBBWthEGVvUC2d2-BNdzfjoQg&maxResults=50&key=AIzaSyB5pjVBKpE_0br9gD66eohoQsYcEyQzlvA',
      origin: 'https://youtube.googleapis.com',
      protocol: 'https:',
      username: '',
      password: '',
      host: 'youtube.googleapis.com',
      hostname: 'youtube.googleapis.com',
      port: '',
      pathname: '/youtube/v3/playlistItems',
      search: '?part=snippet%2CcontentDetails%2Cstatus&playlistId=PLIddAt-uiBBWthEGVvUC2d2-BNdzfjoQg&maxResults=50&key=AIzaSyB5pjVBKpE_0br9gD66eohoQsYcEyQzlvA',
      searchParams: URLSearchParams {
        'part' => 'snippet,contentDetails,status',
        'playlistId' => 'PLIddAt-uiBBWthEGVvUC2d2-BNdzfjoQg',
        'maxResults' => '50',
        'key' => 'AIzaSyB5pjVBKpE_0br9gD66eohoQsYcEyQzlvA' },
      hash: ''
    },
    method: 'GET',
    apiVersion: '',
    userAgentDirectives: [ [Object] ],
    paramsSerializer: [Function (anonymous)],
    headers: Headers {
      'accept-encoding': 'gzip',
      'user-agent': 'google-api-nodejs-client/8.0.0 (gzip)',
      'x-goog-api-client': 'gdcl/8.0.0 gl-node/22.14.0'
    },
    params: {
      part: 'snippet,contentDetails,status',
      playlistId: 'PLIddAt-uiBBWthEGVvUC2d2-BNdzfjoQg',
      maxResults: 50,
      key: 'AIzaSyB5pjVBKpE_0br9gD66eohoQsYcEyQzlvA'
    },
    validateStatus: [Function (anonymous)],
    retry: true,
    responseType: 'unknown',
    errorRedactor: [Function: defaultErrorRedactor],
    retryConfig: {
      currentRetryAttempt: 0,
      retry: 3,
      httpMethodsToRetry: [Array],
      noResponseRetries: 2,
      retryDelayMultiplier: 2,
      timeOfFirstRequest: 1761605835328,
      totalTimeout: 9007199254740991,
      maxRetryDelay: 9007199254740991,
      statusCodesToRetry: [Array]
    }
  },
  response: Response {
    size: 0,
    data: { error: [Object] },
    config: {
      url: URL {},
      method: 'GET',
      apiVersion: '',
      userAgentDirectives: [Array],
      paramsSerializer: [Function (anonymous)],
      headers: Headers {
        'accept-encoding': 'gzip',
        'user-agent': 'google-api-nodejs-client/8.0.0 (gzip)',
        'x-goog-api-client': 'gdcl/8.0.0 gl-node/22.14.0'
      },
      params: [Object],
      validateStatus: [Function (anonymous)],
      retry: true,
      responseType: 'unknown',
      errorRedactor: [Function: defaultErrorRedactor]
    },
    [Symbol(Body internals)]: {
      body: [Gunzip],
      stream: [Gunzip],
      boundary: null,
      disturbed: true,
      error: null
    },
    [Symbol(Response internals)]: {
      type: 'default',
      url: 'https://youtube.googleapis.com/youtube/v3/playlistItems?part=snippet%2CcontentDetails%2Cstatus&playlistId=PLIddAt-uiBBWthEGVvUC2d2-BNdzfjoQg&maxResults=50&key=AIzaSyB5pjVBKpE_0br9gD66eohoQsYcEyQzlvA',
      status: 400,
      statusText: 'Bad Request',
      headers: [Object],
      counter: 0,
      highWaterMark: 16384
    }
  },
  code: 400,
  status: 400,
  error: undefined,
  [Symbol(gaxios-gaxios-error)]: '7.1.2',
  [cause]: {
    message: 'API key expired. Please renew the API key.',
    code: 400,
    status: 'INVALID_ARGUMENT',
    errors: [ [Object] ],
    details: [ [Object], [Object] ]
  }
}
Get playlist videos error GaxiosError: API key expired. Please renew the API key.
    at Gaxios._request (C:\Users\<USER>\Desktop\Chambitas\zonacero\node_modules\gaxios\build\cjs\src\gaxios.js:154:23)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async YouTubeService.getPublicPlaylistVideos (C:\Users\<USER>\Desktop\Chambitas\zonacero\backend\youtube-service.js:198:30)
    at async C:\Users\<USER>\Desktop\Chambitas\zonacero\backend\server.js:977:18 {
  config: {
    url: URL {
      href: 'https://youtube.googleapis.com/youtube/v3/playlistItems?part=snippet%2CcontentDetails%2Cstatus&playlistId=PLIddAt-uiBBWthEGVvUC2d2-BNdzfjoQg&maxResults=50&key=AIzaSyB5pjVBKpE_0br9gD66eohoQsYcEyQzlvA',
      origin: 'https://youtube.googleapis.com',
      protocol: 'https:',
      username: '',
      password: '',
      host: 'youtube.googleapis.com',
      hostname: 'youtube.googleapis.com',
      port: '',
      pathname: '/youtube/v3/playlistItems',
      search: '?part=snippet%2CcontentDetails%2Cstatus&playlistId=PLIddAt-uiBBWthEGVvUC2d2-BNdzfjoQg&maxResults=50&key=AIzaSyB5pjVBKpE_0br9gD66eohoQsYcEyQzlvA',
      searchParams: URLSearchParams {
        'part' => 'snippet,contentDetails,status',
        'playlistId' => 'PLIddAt-uiBBWthEGVvUC2d2-BNdzfjoQg',
        'maxResults' => '50',
        'key' => 'AIzaSyB5pjVBKpE_0br9gD66eohoQsYcEyQzlvA' },
      hash: ''
    },
    method: 'GET',
    apiVersion: '',
    userAgentDirectives: [ [Object] ],
    paramsSerializer: [Function (anonymous)],
    headers: Headers {
      'accept-encoding': 'gzip',
      'user-agent': 'google-api-nodejs-client/8.0.0 (gzip)',
      'x-goog-api-client': 'gdcl/8.0.0 gl-node/22.14.0'
    },
    params: {
      part: 'snippet,contentDetails,status',
      playlistId: 'PLIddAt-uiBBWthEGVvUC2d2-BNdzfjoQg',
      maxResults: 50,
      key: 'AIzaSyB5pjVBKpE_0br9gD66eohoQsYcEyQzlvA'
    },
    validateStatus: [Function (anonymous)],
    retry: true,
    responseType: 'unknown',
    errorRedactor: [Function: defaultErrorRedactor],
    retryConfig: {
      currentRetryAttempt: 0,
      retry: 3,
      httpMethodsToRetry: [Array],
      noResponseRetries: 2,
      retryDelayMultiplier: 2,
      timeOfFirstRequest: 1761605835328,
      totalTimeout: 9007199254740991,
      maxRetryDelay: 9007199254740991,
      statusCodesToRetry: [Array]
    }
  },
  response: Response {
    size: 0,
    data: { error: [Object] },
    config: {
      url: URL {},
      method: 'GET',
      apiVersion: '',
      userAgentDirectives: [Array],
      paramsSerializer: [Function (anonymous)],
      headers: Headers {
        'accept-encoding': 'gzip',
        'user-agent': 'google-api-nodejs-client/8.0.0 (gzip)',
        'x-goog-api-client': 'gdcl/8.0.0 gl-node/22.14.0'
      },
      params: [Object],
      validateStatus: [Function (anonymous)],
      retry: true,
      responseType: 'unknown',
      errorRedactor: [Function: defaultErrorRedactor]
    },
    [Symbol(Body internals)]: {
      body: [Gunzip],
      stream: [Gunzip],
      boundary: null,
      disturbed: true,
      error: null
    },
    [Symbol(Response internals)]: {
      type: 'default',
      url: 'https://youtube.googleapis.com/youtube/v3/playlistItems?part=snippet%2CcontentDetails%2Cstatus&playlistId=PLIddAt-uiBBWthEGVvUC2d2-BNdzfjoQg&maxResults=50&key=AIzaSyB5pjVBKpE_0br9gD66eohoQsYcEyQzlvA',
      status: 400,
      statusText: 'Bad Request',
      headers: [Object],
      counter: 0,
      highWaterMark: 16384
    }
  },
  code: 400,
  status: 400,
  error: undefined,
  [Symbol(gaxios-gaxios-error)]: '7.1.2',
  [cause]: {
    message: 'API key expired. Please renew the API key.',
    code: 400,
    status: 'INVALID_ARGUMENT',
    errors: [ [Object] ],
    details: [ [Object], [Object] ]
  }
}
Error getting public playlist videos: GaxiosError: API key expired. Please renew the API key.
    at Gaxios._request (C:\Users\<USER>\Desktop\Chambitas\zonacero\node_modules\gaxios\build\cjs\src\gaxios.js:154:23)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async YouTubeService.getPublicPlaylistVideos (C:\Users\<USER>\Desktop\Chambitas\zonacero\backend\youtube-service.js:198:30)
    at async C:\Users\<USER>\Desktop\Chambitas\zonacero\backend\server.js:977:18 {
  config: {
    url: URL {
      href: 'https://youtube.googleapis.com/youtube/v3/playlistItems?part=snippet%2CcontentDetails%2Cstatus&playlistId=PLIddAt-uiBBWthEGVvUC2d2-BNdzfjoQg&maxResults=50&key=AIzaSyB5pjVBKpE_0br9gD66eohoQsYcEyQzlvA',
      origin: 'https://youtube.googleapis.com',
      protocol: 'https:',
      username: '',
      password: '',
      host: 'youtube.googleapis.com',
      hostname: 'youtube.googleapis.com',
      port: '',
      pathname: '/youtube/v3/playlistItems',
      search: '?part=snippet%2CcontentDetails%2Cstatus&playlistId=PLIddAt-uiBBWthEGVvUC2d2-BNdzfjoQg&maxResults=50&key=AIzaSyB5pjVBKpE_0br9gD66eohoQsYcEyQzlvA',
      searchParams: URLSearchParams {
        'part' => 'snippet,contentDetails,status',
        'playlistId' => 'PLIddAt-uiBBWthEGVvUC2d2-BNdzfjoQg',
        'maxResults' => '50',
        'key' => 'AIzaSyB5pjVBKpE_0br9gD66eohoQsYcEyQzlvA' },
      hash: ''
    },
    method: 'GET',
    apiVersion: '',
    userAgentDirectives: [ [Object] ],
    paramsSerializer: [Function (anonymous)],
    headers: Headers {
      'accept-encoding': 'gzip',
      'user-agent': 'google-api-nodejs-client/8.0.0 (gzip)',
      'x-goog-api-client': 'gdcl/8.0.0 gl-node/22.14.0'
    },
    params: {
      part: 'snippet,contentDetails,status',
      playlistId: 'PLIddAt-uiBBWthEGVvUC2d2-BNdzfjoQg',
      maxResults: 50,
      key: 'AIzaSyB5pjVBKpE_0br9gD66eohoQsYcEyQzlvA'
    },
    validateStatus: [Function (anonymous)],
    retry: true,
    responseType: 'unknown',
    errorRedactor: [Function: defaultErrorRedactor],
    retryConfig: {
      currentRetryAttempt: 0,
      retry: 3,
      httpMethodsToRetry: [Array],
      noResponseRetries: 2,
      retryDelayMultiplier: 2,
      timeOfFirstRequest: 1761605835403,
      totalTimeout: 9007199254740991,
      maxRetryDelay: 9007199254740991,
      statusCodesToRetry: [Array]
    }
  },
  response: Response {
    size: 0,
    data: { error: [Object] },
    config: {
      url: URL {},
      method: 'GET',
      apiVersion: '',
      userAgentDirectives: [Array],
      paramsSerializer: [Function (anonymous)],
      headers: Headers {
        'accept-encoding': 'gzip',
        'user-agent': 'google-api-nodejs-client/8.0.0 (gzip)',
        'x-goog-api-client': 'gdcl/8.0.0 gl-node/22.14.0'
      },
      params: [Object],
      validateStatus: [Function (anonymous)],
      retry: true,
      responseType: 'unknown',
      errorRedactor: [Function: defaultErrorRedactor]
    },
    [Symbol(Body internals)]: {
      body: [Gunzip],
      stream: [Gunzip],
      boundary: null,
      disturbed: true,
      error: null
    },
    [Symbol(Response internals)]: {
      type: 'default',
      url: 'https://youtube.googleapis.com/youtube/v3/playlistItems?part=snippet%2CcontentDetails%2Cstatus&playlistId=PLIddAt-uiBBWthEGVvUC2d2-BNdzfjoQg&maxResults=50&key=AIzaSyB5pjVBKpE_0br9gD66eohoQsYcEyQzlvA',
      status: 400,
      statusText: 'Bad Request',
      headers: [Object],
      counter: 0,
      highWaterMark: 16384
    }
  },
  code: 400,
  status: 400,
  error: undefined,
  [Symbol(gaxios-gaxios-error)]: '7.1.2',
  [cause]: {
    message: 'API key expired. Please renew the API key.',
    code: 400,
    status: 'INVALID_ARGUMENT',
    errors: [ [Object] ],
    details: [ [Object], [Object] ]
  }
}
Get playlist videos error GaxiosError: API key expired. Please renew the API key.
    at Gaxios._request (C:\Users\<USER>\Desktop\Chambitas\zonacero\node_modules\gaxios\build\cjs\src\gaxios.js:154:23)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async YouTubeService.getPublicPlaylistVideos (C:\Users\<USER>\Desktop\Chambitas\zonacero\backend\youtube-service.js:198:30)
    at async C:\Users\<USER>\Desktop\Chambitas\zonacero\backend\server.js:977:18 {
  config: {
    url: URL {
      href: 'https://youtube.googleapis.com/youtube/v3/playlistItems?part=snippet%2CcontentDetails%2Cstatus&playlistId=PLIddAt-uiBBWthEGVvUC2d2-BNdzfjoQg&maxResults=50&key=AIzaSyB5pjVBKpE_0br9gD66eohoQsYcEyQzlvA',
      origin: 'https://youtube.googleapis.com',
      protocol: 'https:',
      username: '',
      password: '',
      host: 'youtube.googleapis.com',
      hostname: 'youtube.googleapis.com',
      port: '',
      pathname: '/youtube/v3/playlistItems',
      search: '?part=snippet%2CcontentDetails%2Cstatus&playlistId=PLIddAt-uiBBWthEGVvUC2d2-BNdzfjoQg&maxResults=50&key=AIzaSyB5pjVBKpE_0br9gD66eohoQsYcEyQzlvA',
      searchParams: URLSearchParams {
        'part' => 'snippet,contentDetails,status',
        'playlistId' => 'PLIddAt-uiBBWthEGVvUC2d2-BNdzfjoQg',
        'maxResults' => '50',
        'key' => 'AIzaSyB5pjVBKpE_0br9gD66eohoQsYcEyQzlvA' },
      hash: ''
    },
    method: 'GET',
    apiVersion: '',
    userAgentDirectives: [ [Object] ],
    paramsSerializer: [Function (anonymous)],
    headers: Headers {
      'accept-encoding': 'gzip',
      'user-agent': 'google-api-nodejs-client/8.0.0 (gzip)',
      'x-goog-api-client': 'gdcl/8.0.0 gl-node/22.14.0'
    },
    params: {
      part: 'snippet,contentDetails,status',
      playlistId: 'PLIddAt-uiBBWthEGVvUC2d2-BNdzfjoQg',
      maxResults: 50,
      key: 'AIzaSyB5pjVBKpE_0br9gD66eohoQsYcEyQzlvA'
    },
    validateStatus: [Function (anonymous)],
    retry: true,
    responseType: 'unknown',
    errorRedactor: [Function: defaultErrorRedactor],
    retryConfig: {
      currentRetryAttempt: 0,
      retry: 3,
      httpMethodsToRetry: [Array],
      noResponseRetries: 2,
      retryDelayMultiplier: 2,
      timeOfFirstRequest: 1761605835403,
      totalTimeout: 9007199254740991,
      maxRetryDelay: 9007199254740991,
      statusCodesToRetry: [Array]
    }
  },
  response: Response {
    size: 0,
    data: { error: [Object] },
    config: {
      url: URL {},
      method: 'GET',
      apiVersion: '',
      userAgentDirectives: [Array],
      paramsSerializer: [Function (anonymous)],
      headers: Headers {
        'accept-encoding': 'gzip',
        'user-agent': 'google-api-nodejs-client/8.0.0 (gzip)',
        'x-goog-api-client': 'gdcl/8.0.0 gl-node/22.14.0'
      },
      params: [Object],
      validateStatus: [Function (anonymous)],
      retry: true,
      responseType: 'unknown',
      errorRedactor: [Function: defaultErrorRedactor]
    },
    [Symbol(Body internals)]: {
      body: [Gunzip],
      stream: [Gunzip],
      boundary: null,
      disturbed: true,
      error: null
    },
    [Symbol(Response internals)]: {
      type: 'default',
      url: 'https://youtube.googleapis.com/youtube/v3/playlistItems?part=snippet%2CcontentDetails%2Cstatus&playlistId=PLIddAt-uiBBWthEGVvUC2d2-BNdzfjoQg&maxResults=50&key=AIzaSyB5pjVBKpE_0br9gD66eohoQsYcEyQzlvA',
      status: 400,
      statusText: 'Bad Request',
      headers: [Object],
      counter: 0,
      highWaterMark: 16384
    }
  },
  code: 400,
  status: 400,
  error: undefined,
  [Symbol(gaxios-gaxios-error)]: '7.1.2',
  [cause]: {
    message: 'API key expired. Please renew the API key.',
    code: 400,
    status: 'INVALID_ARGUMENT',
    errors: [ [Object] ],
    details: [ [Object], [Object] ]
  }
}
