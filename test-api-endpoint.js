const axios = require('axios');

async function testApiEndpoint() {
    console.log('🔍 Testing API endpoint that\'s causing the 500 error...\n');
    
    const baseURL = 'http://localhost:4000';
    const playlistId = '34146279-4e57-4e7b-886c-3ecd0c7b0ebc';
    const userId = '904fc89d-a760-46f5-afcb-080d94925b0a';
    
    try {
        // First, test if the server is running
        console.log('1. Testing server health...');
        const healthResponse = await axios.get(`${baseURL}/api/health`);
        console.log('✅ Server is running:', healthResponse.data);
        
        // Test the specific endpoint that's failing
        console.log('\n2. Testing the failing endpoint...');
        console.log(`Endpoint: POST ${baseURL}/api/admin/playlists/${playlistId}/users/${userId}`);
        
        // Note: This will likely fail due to authentication, but we can see the error details
        try {
            const response = await axios.post(`${baseURL}/api/admin/playlists/${playlistId}/users/${userId}`);
            console.log('✅ Unexpected success:', response.data);
        } catch (err) {
            console.log('❌ Expected error (authentication/authorization):');
            console.log('Status:', err.response?.status);
            console.log('Error:', err.response?.data);
            
            if (err.response?.status === 401) {
                console.log('✅ This is expected - authentication required');
            } else if (err.response?.status === 500) {
                console.log('❌ This is the 500 error we need to fix!');
                console.log('Error details:', err.response?.data);
            }
        }
        
        // Test the DELETE endpoint as well
        console.log('\n3. Testing the DELETE endpoint...');
        console.log(`Endpoint: DELETE ${baseURL}/api/admin/playlists/${playlistId}/users/${userId}`);
        
        try {
            const response = await axios.delete(`${baseURL}/api/admin/playlists/${playlistId}/users/${userId}`);
            console.log('✅ Unexpected success:', response.data);
        } catch (err) {
            console.log('❌ Expected error (authentication/authorization):');
            console.log('Status:', err.response?.status);
            console.log('Error:', err.response?.data);
            
            if (err.response?.status === 401) {
                console.log('✅ This is expected - authentication required');
            } else if (err.response?.status === 500) {
                console.log('❌ This is the 500 error we need to fix!');
                console.log('Error details:', err.response?.data);
            }
        }
        
    } catch (err) {
        console.error('❌ Server connection failed:', err.message);
        console.log('\n💡 Make sure the server is running with: npm run server');
    }
}

testApiEndpoint();
